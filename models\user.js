const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { pool } = require('../db');

class User {
    constructor(userData) {
        this.id = userData.id || uuidv4();
        this.name = userData.name;
        this.age = userData.age;
        this.email = userData.email;
        this.mobile = userData.mobile;
        this.address = userData.address;
        this.aadharCardNumber = userData.aadharCardNumber;
        this.password = userData.password;
        this.role = userData.role || 'voter';
    }

    // Hash password before saving
    async hashPassword() {
        if (this.password) {
            const salt = await bcrypt.genSalt(10);
            this.password = await bcrypt.hash(this.password, salt);
        }
    }

    // Compare password
    static async comparePassword(candidatePassword, hashedPassword) {
        try {
            return await bcrypt.compare(candidatePassword, hashedPassword);
        } catch (err) {
            throw err;
        }
    }

    // Validate user data
    validate() {
        const errors = [];

        if (!this.name) errors.push('Name is required');
        if (!this.age) errors.push('Age is required');
        if (!this.mobile) errors.push('Mobile is required');
        if (!this.address) errors.push('Address is required');
        if (!this.aadharCardNumber) errors.push('Aadhar Card Number is required');
        if (!this.password) errors.push('Password is required');

        // Validate Aadhar Card Number (12 digits)
        if (this.aadharCardNumber && !/^\d{12}$/.test(this.aadharCardNumber)) {
            errors.push('Aadhar Card Number must be exactly 12 digits');
        }

        // Validate role
        if (this.role && !['voter', 'admin'].includes(this.role)) {
            errors.push('Role must be either voter or admin');
        }

        return errors;
    }

    // Save user to database
    async save() {
        const validationErrors = this.validate();
        if (validationErrors.length > 0) {
            throw new Error(validationErrors.join(', '));
        }

        // Hash password before saving
        await this.hashPassword();

        const query = `
            INSERT INTO users (id, name, age, email, mobile, address, aadhar_card_number, password, role)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            this.id,
            this.name,
            this.age,
            this.email,
            this.mobile,
            this.address,
            this.aadharCardNumber,
            this.password,
            this.role
        ];

        try {
            await pool.query(query, values);
            return this;
        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                throw new Error('User with the same Aadhar Card Number already exists');
            }
            throw error;
        }
    }

    // Find user by ID
    static async findById(id) {
        const query = 'SELECT * FROM users WHERE id = ?';
        try {
            const [rows] = await pool.query(query, [id]);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    // Find user by Aadhar Card Number
    static async findByAadhar(aadharCardNumber) {
        const query = 'SELECT * FROM users WHERE aadhar_card_number = ?';
        try {
            const [rows] = await pool.query(query, [aadharCardNumber]);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    // Find user by role
    static async findByRole(role) {
        const query = 'SELECT * FROM users WHERE role = ?';
        try {
            const [rows] = await pool.query(query, [role]);
            return rows;
        } catch (error) {
            throw error;
        }
    }

    // Find one user by criteria
    static async findOne(criteria) {
        let query = 'SELECT * FROM users WHERE ';
        const conditions = [];
        const values = [];

        for (const [key, value] of Object.entries(criteria)) {
            const dbKey = key === 'aadharCardNumber' ? 'aadhar_card_number' : key;
            conditions.push(`${dbKey} = ?`);
            values.push(value);
        }

        query += conditions.join(' AND ');

        try {
            const [rows] = await pool.query(query, values);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    // Get user's voting history
    static async getVotingHistory(userId) {
        const query = `
            SELECT vh.year, vh.party_voted, c.name as candidate_name, c.party, vh.voted_at
            FROM voting_history vh
            JOIN candidates c ON vh.party_voted = c.id
            WHERE vh.user_id = ?
            ORDER BY vh.year DESC
        `;
        try {
            const [rows] = await pool.query(query, [userId]);
            return rows;
        } catch (error) {
            throw error;
        }
    }

    // Add voting history entry
    static async addVotingHistory(userId, year, candidateId) {
        const query = `
            INSERT INTO voting_history (id, user_id, year, party_voted)
            VALUES (?, ?, ?, ?)
        `;
        const values = [uuidv4(), userId, year, candidateId];

        try {
            await pool.query(query, values);
            return true;
        } catch (error) {
            throw error;
        }
    }

    // Update user password
    static async updatePassword(userId, newPassword) {
        try {
            // Hash the new password
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(newPassword, salt);

            const query = 'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
            const [result] = await pool.query(query, [hashedPassword, userId]);

            return result.affectedRows > 0;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = User;
