const express = require('express');
const router = express.Router();
const { pool } = require('../db');

/**
 * Health check routes for monitoring database and API status
 */

// Basic health check
router.get('/health', async (req, res) => {
    try {
        // Test database connection
        const [rows] = await pool.query('SELECT 1 as status');

        const healthStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            database: 'connected',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: process.version
        };

        res.status(200).json(healthStatus);
    } catch (error) {
        const healthStatus = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            database: 'disconnected',
            error: error.message,
            uptime: process.uptime()
        };

        res.status(503).json(healthStatus);
    }
});

// Database status check
router.get('/health/database', async (req, res) => {
    try {
        // Check all tables exist
        const tables = ['users', 'candidates', 'votes', 'vote_counts_by_year', 'voting_history', 'voting_times'];
        const tableStatus = {};
        
        for (const table of tables) {
            try {
                const [rows] = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
                tableStatus[table] = {
                    exists: true,
                    count: rows[0].count
                };
            } catch (error) {
                tableStatus[table] = {
                    exists: false,
                    error: error.message
                };
            }
        }

        // Check triggers exist
        const [triggerRows] = await pool.query(`
            SELECT TRIGGER_NAME
            FROM information_schema.TRIGGERS
            WHERE TRIGGER_SCHEMA = DATABASE()
        `);

        const triggers = triggerRows.map(row => row.TRIGGER_NAME);

        const dbStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            database: process.env.DB_NAME || 'voting_app',
            tables: tableStatus,
            triggers: triggers
        };
        
        res.status(200).json(dbStatus);
    } catch (error) {
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// API endpoints status
router.get('/health/api', async (req, res) => {
    try {
        // Test each major functionality
        const [userCount] = await pool.query('SELECT COUNT(*) as count FROM users');
        const [candidateCount] = await pool.query('SELECT COUNT(*) as count FROM candidates');
        const [voteCount] = await pool.query('SELECT COUNT(*) as count FROM votes');
        const [votingTimeCount] = await pool.query('SELECT COUNT(*) as count FROM voting_times');
        
        const apiStatus = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            endpoints: {
                user_management: 'operational',
                candidate_management: 'operational',
                voting_system: 'operational',
                admin_functions: 'operational',
                results_system: 'operational'
            },
            data_summary: {
                total_users: userCount[0].count,
                total_candidates: candidateCount[0].count,
                total_votes: voteCount[0].count,
                voting_periods: votingTimeCount[0].count
            }
        };
        
        res.status(200).json(apiStatus);
    } catch (error) {
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

// Detailed system info for debugging
router.get('/health/system', async (req, res) => {
    try {
        const systemInfo = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: {
                node_version: process.version,
                platform: process.platform,
                arch: process.arch,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                cpu_usage: process.cpuUsage()
            },
            database: {
                host: process.env.DB_HOST || 'localhost',
                port: process.env.DB_PORT || 3306,
                database: process.env.DB_NAME || 'voting_app',
                user: process.env.DB_USER || 'root'
            },
            features: {
                uuid_support: true,
                triggers_enabled: true,
                foreign_keys: true,
                auto_initialization: true
            }
        };
        
        res.status(200).json(systemInfo);
    } catch (error) {
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            error: error.message
        });
    }
});

module.exports = router;
