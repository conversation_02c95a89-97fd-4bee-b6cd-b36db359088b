const express = require('express');
const router = express.Router();
const User = require('../models/user');
const {jwtAuthMiddleware, generateToken} = require('../jwt');
const Candidate = require('../models/candidate');
const VotingTime = require('../models/votingTime');


const checkAdminRole = async (userID) => {
   try{
        const user = await User.findById(userID);
        if(user && user.role === 'admin'){
            return true;
        }
        return false;
   }catch(err){
        return false;
   }
}

// POST route to add a candidate
router.post('/', jwtAuthMiddleware, async (req, res) =>{
    const { name, party, age } = req.body;

    try {
        if(!(await checkAdminRole(req.user.id)))
            return res.status(403).json({message: 'user does not have admin role'});

        // Validate request body
        if (!name || !party || !age) {
            return res.status(400).json({ error: 'All fields (name, party, age) are required.' });
        }
        // Create a new candidate instance
        const newCandidate = new Candidate({
            name,
            party,
            age
        });

        // Save the candidate to the database
        await newCandidate.save();

        res.status(201).json({ message: 'Candidate created successfully', candidate: newCandidate });
    }
    catch(err){
        console.log(err);
        res.status(500).json({error: 'Internal Server Error'});
    }
})

router.put('/:candidateID', jwtAuthMiddleware, async (req, res)=>{
    try{
        if(!(await checkAdminRole(req.user.id)))
            return res.status(403).json({message: 'user does not have admin role'});

        const candidateID = req.params.candidateID; // Extract the id from the URL parameter
        const updatedCandidateData = req.body; // Updated data for the person

        const response = await Candidate.findByIdAndUpdate(candidateID, updatedCandidateData, {
            new: true // Return the updated document
        })

        if (!response) {
            return res.status(404).json({ error: 'Candidate not found' });
        }

        console.log('candidate data updated');
        res.status(200).json(response);
    }catch(err){
        console.log(err);
        res.status(500).json({error: 'Internal Server Error'});
    }
})

router.delete('/:candidateID', jwtAuthMiddleware, async (req, res)=>{
    try{
        if(!(await checkAdminRole(req.user.id)))
            return res.status(403).json({message: 'user does not have admin role'});

        const candidateID = req.params.candidateID; // Extract the id from the URL parameter

        const response = await Candidate.findByIdAndDelete(candidateID);

        if (!response) {
            return res.status(404).json({ error: 'Candidate not found' });
        }

        console.log('candidate deleted');
        res.status(200).json(response);
    }catch(err){
        console.log(err);
        res.status(500).json({error: 'Internal Server Error'});
    }
})

// let's start voting
router.post('/vote', jwtAuthMiddleware, async (req, res)=>{
    // no admin can vote
    // user can only vote once
    const { candidateId } = req.body;
    const currentYear = new Date().getFullYear();
    const userId = req.user.id;

    try {
        // Get user details
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        // Check if user is admin (admins cannot vote)
        if (user.role === 'admin') {
            return res.status(403).json({ message: "Admins are not allowed to vote." });
        }

        // Check if user has already voted this year
        const hasVoted = await Candidate.hasUserVoted(userId, currentYear);
        if (hasVoted) {
            return res.status(400).json({ message: "You've already voted this year." });
        }

        // Validate voting time for the current year
        const isVotingActive = await VotingTime.isVotingActive(currentYear);
        if (!isVotingActive) {
            return res.status(400).json({ message: "Voting is not currently active." });
        }

        // Check if candidate exists
        const candidate = await Candidate.findById(candidateId);
        if (!candidate) {
            return res.status(404).json({ message: "Candidate not found." });
        }

        // Record the vote
        await Candidate.addVote(candidateId, userId, currentYear);

        // Add to user's voting history
        await User.addVotingHistory(userId, currentYear, candidateId);

        res.status(200).json({ message: "Vote recorded successfully!" });
    }  catch (error) {
        console.error('Error registering vote:', error);
        res.status(500).json({ error: 'An error occurred while registering the vote' });
    }
});


// Get List of all candidates with only name and party fields
router.get('/', async (req, res) => {
    try {
        // Find all candidates and select only the name and party fields
        const candidates = await Candidate.find('id, name, party');

        // Return the list of candidates
        res.status(200).json(candidates);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal Server Error' });
    }
});


router.get('/total-vote-count', jwtAuthMiddleware, async (req, res) => {
    try {
        // Retrieve all candidates with their total vote counts
        const totalVoteCounts = await Candidate.getVoteCounts();

        res.status(200).json(totalVoteCounts);
    } catch (error) {
        console.error('Error retrieving total vote count:', error);
        res.status(500).json({ error: 'An error occurred while retrieving total vote counts' });
    }
});

module.exports = router;