const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcrypt');

/**
 * Simple database initialization
 */
const initializeDatabase = async (db) => {
    console.log('🔄 Initializing database...');

    try {
        // Create database if not exists
        const dbName = process.env.DB_NAME || 'voting_app';
        await db.execute(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
        await db.execute(`USE ${dbName}`);
        console.log(`✅ Database '${dbName}' ready`);

        // Create tables
        await createTables(db);

        // Insert initial data
        await insertInitialData(db);

        console.log('✅ Database initialization completed!');
    } catch (error) {
        console.error('❌ Database initialization failed:', error);
        throw error;
    }
};

/**
 * Create all tables
 */
const createTables = async (db) => {
    console.log('📝 Creating tables...');

    // Users table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            name VARCHAR(255) NOT NULL,
            age INT NOT NULL,
            email VARCHAR(255),
            mobile VARCHAR(20) NOT NULL,
            address TEXT NOT NULL,
            aadhar_card_number VARCHAR(12) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('voter', 'admin') DEFAULT 'voter',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ Users table ready');

    // Candidates table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS candidates (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            name VARCHAR(255) NOT NULL,
            party VARCHAR(255) NOT NULL,
            age INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ Candidates table ready');

    // Votes table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS votes (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            candidate_id VARCHAR(36) NOT NULL,
            voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            year INT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_year (user_id, year)
        )
    `);
    console.log('✅ Votes table ready');

    // Vote counts by year table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS vote_counts_by_year (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            candidate_id VARCHAR(36) NOT NULL,
            year INT NOT NULL,
            count INT DEFAULT 0,
            FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
            UNIQUE KEY unique_candidate_year (candidate_id, year)
        )
    `);
    console.log('✅ Vote counts table ready');

    // Voting history table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS voting_history (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            user_id VARCHAR(36) NOT NULL,
            year INT NOT NULL,
            party_voted VARCHAR(36) NOT NULL,
            voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (party_voted) REFERENCES candidates(id) ON DELETE CASCADE
        )
    `);
    console.log('✅ Voting history table ready');

    // Voting times table
    await db.execute(`
        CREATE TABLE IF NOT EXISTS voting_times (
            id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
            year INT NOT NULL UNIQUE,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            daily_start_time TIME NOT NULL,
            daily_end_time TIME NOT NULL,
            results_published BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `);
    console.log('✅ Voting times table ready');
};

/**
 * Insert initial data if tables are empty
 */
const insertInitialData = async (db) => {
    console.log('📝 Checking for initial data...');

    try {
        // Check if we have any users
        const [userRows] = await db.execute('SELECT COUNT(*) as count FROM users');
        const userCount = userRows[0].count;

        if (userCount === 0) {
            console.log('📝 Creating initial admin user...');
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash('admin123', salt);

            const adminId = uuidv4();
            const query = `
                INSERT INTO users (id, name, age, email, mobile, address, aadhar_card_number, password, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const values = [
                adminId,
                'System Admin',
                30,
                '<EMAIL>',
                '9999999999',
                'System Address',
                '************',
                hashedPassword,
                'admin'
            ];

            await db.execute(query, values);
            console.log('✅ Admin user created (aadhar: ************, password: admin123)');
        }

        // Check if we have any candidates
        const [candidateRows] = await db.execute('SELECT COUNT(*) as count FROM candidates');
        const candidateCount = candidateRows[0].count;

        if (candidateCount === 0) {
            console.log('📝 Creating sample candidates...');
            const candidates = [
                { name: 'Rajesh Kumar', party: 'Democratic Party', age: 45 },
                { name: 'Priya Sharma', party: 'Progressive Alliance', age: 38 },
                { name: 'Amit Singh', party: 'People\'s Party', age: 52 }
            ];

            for (const candidate of candidates) {
                const candidateId = uuidv4();
                const query = `
                    INSERT INTO candidates (id, name, party, age)
                    VALUES (?, ?, ?, ?)
                `;

                await db.execute(query, [candidateId, candidate.name, candidate.party, candidate.age]);
            }

            console.log('✅ Sample candidates created');
        }

        console.log('✅ Initial data ready');
    } catch (error) {
        console.error('❌ Error inserting initial data:', error.message);
    }
};

module.exports = { initializeDatabase };
