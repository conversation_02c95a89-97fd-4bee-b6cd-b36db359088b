const mysql = require('mysql2/promise');
require('dotenv').config();

const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'voting_app',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// All schema creation queries
const tables = [
    `CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(255) NOT NULL,
        age INT NOT NULL,
        email VARCHAR(255),
        mobile VARCHAR(20) NOT NULL,
        address TEXT NOT NULL,
        aadhar_card_number VARCHAR(12) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('voter', 'admin') DEFAULT 'voter',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS candidates (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        name VARCHAR(255) NOT NULL,
        party VARCHAR(255) NOT NULL,
        age INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )`,

    `CREATE TABLE IF NOT EXISTS votes (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        candidate_id VARCHAR(36) NOT NULL,
        voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        year INT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_year (user_id, year)
    )`,

    `CREATE TABLE IF NOT EXISTS vote_counts_by_year (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        candidate_id VARCHAR(36) NOT NULL,
        year INT NOT NULL,
        count INT DEFAULT 0,
        FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
        UNIQUE KEY unique_candidate_year (candidate_id, year)
    )`,

    `CREATE TABLE IF NOT EXISTS voting_history (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        user_id VARCHAR(36) NOT NULL,
        year INT NOT NULL,
        party_voted VARCHAR(36) NOT NULL,
        voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (party_voted) REFERENCES candidates(id) ON DELETE CASCADE
    )`,

    `CREATE TABLE IF NOT EXISTS voting_times (
        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
        year INT NOT NULL UNIQUE,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        daily_start_time TIME NOT NULL,
        daily_end_time TIME NOT NULL,
        results_published BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )`
];

// Create tables using async/await
const createTables = async () => {
    for (let i = 0; i < tables.length; i++) {
        try {
            await pool.query(tables[i]);
            console.log(`✅ Table ${i + 1} created or already exists.`);
        } catch (err) {
            console.error(`❌ Error creating table ${i + 1}:`, err.message);
            throw err;
        }
    }
};

// Insert initial data if tables are empty
const insertInitialData = async () => {
    const bcrypt = require('bcrypt');
    const { v4: uuidv4 } = require('uuid');

    try {
        // Check if we have any users
        const [userRows] = await pool.query('SELECT COUNT(*) as count FROM users');
        const userCount = userRows[0].count;

        if (userCount === 0) {
            console.log('📝 Creating initial admin user...');
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash('admin123', salt);

            const adminId = uuidv4();
            const query = `
                INSERT INTO users (id, name, age, email, mobile, address, aadhar_card_number, password, role)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const values = [
                adminId,
                'System Admin',
                30,
                '<EMAIL>',
                '9999999999',
                'System Address',
                '************',
                hashedPassword,
                'admin'
            ];

            await pool.query(query, values);
            console.log('✅ Admin user created (aadhar: ************, password: admin123)');
        }

        // Check if we have any candidates
        const [candidateRows] = await pool.query('SELECT COUNT(*) as count FROM candidates');
        const candidateCount = candidateRows[0].count;

        if (candidateCount === 0) {
            console.log('📝 Creating sample candidates...');
            const candidates = [
                { name: 'Rajesh Kumar', party: 'Democratic Party', age: 45 },
                { name: 'Priya Sharma', party: 'Progressive Alliance', age: 38 },
                { name: 'Amit Singh', party: 'People\'s Party', age: 52 }
            ];

            for (const candidate of candidates) {
                const candidateId = uuidv4();
                const query = `
                    INSERT INTO candidates (id, name, party, age)
                    VALUES (?, ?, ?, ?)
                `;

                await pool.query(query, [candidateId, candidate.name, candidate.party, candidate.age]);
            }

            console.log('✅ Sample candidates created');
        }

        console.log('✅ Initial data ready');
    } catch (error) {
        console.error('❌ Error inserting initial data:', error.message);
    }
};

const connectDB = async () => {
    try {
        await createTables();
        await insertInitialData();
        console.log('✅ Database connected and ready');
    } catch (error) {
        console.error('❌ Error during DB setup:', error);
        process.exit(1);
    }
};

module.exports = { pool, connectDB };

