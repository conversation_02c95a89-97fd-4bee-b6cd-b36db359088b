const { v4: uuidv4 } = require('uuid');
const { pool } = require('../db');

class Candidate {
    constructor(candidateData) {
        this.id = candidateData.id || uuidv4();
        this.name = candidateData.name;
        this.party = candidateData.party;
        this.age = candidateData.age;
    }

    // Validate candidate data
    validate() {
        const errors = [];

        if (!this.name) errors.push('Name is required');
        if (!this.party) errors.push('Party is required');
        if (!this.age) errors.push('Age is required');

        return errors;
    }

    // Save candidate to database
    async save() {
        const validationErrors = this.validate();
        if (validationErrors.length > 0) {
            throw new Error(validationErrors.join(', '));
        }

        const query = `
            INSERT INTO candidates (id, name, party, age)
            VALUES (?, ?, ?, ?)
        `;

        const values = [this.id, this.name, this.party, this.age];

        try {
            await pool.execute(query, values);
            return this;
        } catch (error) {
            throw error;
        }
    }

    // Find candidate by ID
    static async findById(id) {
        const query = 'SELECT * FROM candidates WHERE id = ?';
        try {
            const [rows] = await pool.execute(query, [id]);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    // Find all candidates
    static async find(projection = '*') {
        const query = `SELECT ${projection} FROM candidates ORDER BY name`;
        try {
            const [rows] = await pool.execute(query);
            return rows;
        } catch (error) {
            throw error;
        }
    }

    // Update candidate by ID
    static async findByIdAndUpdate(id, updateData, options = {}) {
        const allowedFields = ['name', 'party', 'age'];
        const updateFields = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            if (allowedFields.includes(key)) {
                updateFields.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updateFields.length === 0) {
            throw new Error('No valid fields to update');
        }

        values.push(id); // Add ID for WHERE clause

        const query = `
            UPDATE candidates
            SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;

        try {
            const [result] = await pool.execute(query, values);

            if (result.affectedRows === 0) {
                return null; // Candidate not found
            }

            if (options.new) {
                return await Candidate.findById(id);
            }

            return { affectedRows: result.affectedRows };
        } catch (error) {
            throw error;
        }
    }

    // Delete candidate by ID
    static async findByIdAndDelete(id) {
        try {
            // First get the candidate data before deletion
            const candidate = await Candidate.findById(id);
            if (!candidate) return null;

            const query = 'DELETE FROM candidates WHERE id = ?';
            const [result] = await pool.execute(query, [id]);

            if (result.affectedRows === 0) {
                return null;
            }

            return candidate;
        } catch (error) {
            throw error;
        }
    }

    // Add a vote for this candidate
    static async addVote(candidateId, userId, year) {
        const connection = await pool.getConnection();

        try {
            await connection.beginTransaction();

            // Insert vote record
            const voteQuery = `
                INSERT INTO votes (id, user_id, candidate_id, year)
                VALUES (?, ?, ?, ?)
            `;
            await connection.execute(voteQuery, [uuidv4(), userId, candidateId, year]);

            // The trigger will automatically update vote_counts_by_year table

            await connection.commit();
            return true;
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    // Get vote counts for all candidates
    static async getVoteCounts() {
        const query = `
            SELECT
                c.id,
                c.name,
                c.party,
                COALESCE(SUM(vcby.count), 0) as totalVotes
            FROM candidates c
            LEFT JOIN vote_counts_by_year vcby ON c.id = vcby.candidate_id
            GROUP BY c.id, c.name, c.party
            ORDER BY totalVotes DESC, c.name
        `;

        try {
            const [rows] = await pool.execute(query);
            return rows.map(row => ({
                name: row.name,
                party: row.party,
                totalVotes: parseInt(row.totalVotes)
            }));
        } catch (error) {
            throw error;
        }
    }

    // Get vote counts by year
    static async getVoteCountsByYear(year) {
        const query = `
            SELECT
                c.id,
                c.name,
                c.party,
                COALESCE(vcby.count, 0) as votes
            FROM candidates c
            LEFT JOIN vote_counts_by_year vcby ON c.id = vcby.candidate_id AND vcby.year = ?
            ORDER BY votes DESC, c.name
        `;

        try {
            const [rows] = await pool.execute(query, [year]);
            return rows.map(row => ({
                name: row.name,
                party: row.party,
                votes: parseInt(row.votes)
            }));
        } catch (error) {
            throw error;
        }
    }

    // Check if user has already voted in a specific year
    static async hasUserVoted(userId, year) {
        const query = 'SELECT COUNT(*) as count FROM votes WHERE user_id = ? AND year = ?';
        try {
            const [rows] = await pool.execute(query, [userId, year]);
            return rows[0].count > 0;
        } catch (error) {
            throw error;
        }
    }

    // Get candidates with their vote counts for a specific year
    static async getCandidatesWithVotes(year) {
        const query = `
            SELECT
                c.*,
                COALESCE(vcby.count, 0) as voteCount
            FROM candidates c
            LEFT JOIN vote_counts_by_year vcby ON c.id = vcby.candidate_id AND vcby.year = ?
            ORDER BY voteCount DESC, c.name
        `;

        try {
            const [rows] = await pool.execute(query, [year]);
            return rows;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = Candidate;