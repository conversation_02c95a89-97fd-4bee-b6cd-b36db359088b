# 🗳️ Voting Application API

A comprehensive voting system API built with Node.js, Express, and MySQL. Features automatic database initialization, UUID-based primary keys, and cloud-ready deployment.

## ✨ Features

- **🔄 Auto Database Initialization**: Automatically creates database, tables, indexes, and triggers on startup
- **🆔 UUID v4 Primary Keys**: Uses UUID v4 for all primary keys for better scalability
- **🔐 Secure Authentication**: JWT-based authentication with bcrypt password hashing
- **🗳️ Complete Voting System**: User registration, candidate management, voting, and results
- **👨‍💼 Admin Panel**: Admin-only functions for candidate and voting time management
- **📊 Health Monitoring**: Comprehensive health check endpoints
- **☁️ Cloud Ready**: Optimized for deployment on platforms like Aiven
- **🌐 Platform Ready**: Easy deployment on any Node.js hosting platform

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- MySQL 8.0+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vote_api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.sample .env
   # Edit .env with your database credentials
   ```

4. **Start the application**
   ```bash
   npm start
   ```

The application will automatically:
- Create the database if it doesn't exist
- Create all necessary tables
- Set up indexes and triggers
- Insert initial admin user and sample data

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
PORT=3000

# MySQL Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=voting_app
DB_PORT=3306

# JWT Secret
JWT_SECRET=your_jwt_secret_key
```

### Default Admin Credentials

After first startup, use these credentials:
- **Aadhar Number**: `************`
- **Password**: `admin123`

⚠️ **Important**: Change the admin password immediately after first login!

## 📚 API Documentation

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/user/signup` | Register new user |
| POST | `/user/login` | User login |
| GET | `/user/profile` | Get user profile (auth required) |
| PUT | `/user/profile/password` | Change password (auth required) |

### Candidate Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/candidate` | Get all candidates |
| POST | `/candidate` | Add candidate (admin only) |
| PUT | `/candidate/:id` | Update candidate (admin only) |
| DELETE | `/candidate/:id` | Delete candidate (admin only) |
| GET | `/candidate/total-vote-count` | Get vote counts (auth required) |

### Voting

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/candidate/vote` | Cast vote (auth required) |

### Admin Functions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/admin/voting-time` | Set voting period (admin only) |
| POST | `/admin/publish-result` | Publish results (admin only) |

### Results

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/results/:year` | Get results for specific year |

### Health Monitoring

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Overall health status |
| GET | `/health/database` | Database status and table info |
| GET | `/health/api` | API functionality status |
| GET | `/health/system` | System information |

## 🗄️ Database Schema

The application automatically creates the following tables:

- **users**: User information and authentication
- **candidates**: Candidate information
- **votes**: Individual vote records
- **vote_counts_by_year**: Aggregated vote counts (auto-updated)
- **voting_history**: User voting history
- **voting_times**: Voting period configuration

## ☁️ Cloud Deployment

### Aiven MySQL

1. Create MySQL service on [Aiven Console](https://console.aiven.io/)
2. Get connection details
3. Update `.env` with Aiven credentials
4. Deploy your application

See [CLOUD_DEPLOYMENT.md](CLOUD_DEPLOYMENT.md) for detailed instructions.

## 🔍 Monitoring

### Health Checks

The application provides comprehensive health monitoring:

```bash
# Check overall health
curl http://localhost:3000/health

# Check database status
curl http://localhost:3000/health/database

# Check API functionality
curl http://localhost:3000/health/api
```

## 🧪 Testing

### Available Scripts

```bash
npm start          # Start with auto-initialization
npm run dev        # Development mode with nodemon
npm run server     # Start server only (no initialization)
npm run init-db    # Initialize database only
npm run health-check # Quick health check
```

---

**Made with ❤️ for secure and scalable voting systems**
