// Test script to verify MySQL migration
require('dotenv').config();
const { pool } = require('./db');
const { initializeDatabase } = require('./database/init');
const User = require('./models/user');
const Candidate = require('./models/candidate');
const VotingTime = require('./models/votingTime');

async function testMigration() {
    console.log('🚀 Testing MySQL Migration...\n');

    try {
        // Initialize database first
        console.log('0. Initializing database...');
        await initializeDatabase(pool);
        console.log('✅ Database initialized\n');
        // Test 1: Create a test user
        console.log('1. Testing User Creation...');
        const testUser = new User({
            name: 'Test User',
            age: 25,
            email: '<EMAIL>',
            mobile: '1234567890',
            address: 'Test Address',
            aadharCardNumber: '123456789012',
            password: 'testpassword',
            role: 'voter'
        });

        const savedUser = await testUser.save();
        console.log('✅ User created successfully:', savedUser.id);

        // Test 2: Find user by Aadhar
        console.log('\n2. Testing User Lookup...');
        const foundUser = await User.findByAadhar('123456789012');
        console.log('✅ User found:', foundUser ? foundUser.name : 'Not found');

        // Test 3: Create a test candidate
        console.log('\n3. Testing Candidate Creation...');
        const testCandidate = new Candidate({
            name: 'Test Candidate',
            party: 'Test Party',
            age: 35
        });

        const savedCandidate = await testCandidate.save();
        console.log('✅ Candidate created successfully:', savedCandidate.id);

        // Test 4: Get all candidates
        console.log('\n4. Testing Candidate Retrieval...');
        const candidates = await Candidate.find();
        console.log('✅ Found candidates:', candidates.length);

        // Test 5: Create voting time
        console.log('\n5. Testing Voting Time Creation...');
        const currentYear = new Date().getFullYear();
        const votingTime = new VotingTime({
            year: currentYear,
            startDate: '2024-01-01',
            endDate: '2024-12-31',
            dailyStartTime: '09:00',
            dailyEndTime: '18:00'
        });

        const savedVotingTime = await votingTime.save();
        console.log('✅ Voting time created successfully for year:', currentYear);

        // Test 6: Test voting
        console.log('\n6. Testing Voting Process...');
        const hasVoted = await Candidate.hasUserVoted(savedUser.id, currentYear);
        console.log('✅ User voting status checked:', hasVoted ? 'Already voted' : 'Can vote');

        if (!hasVoted) {
            await Candidate.addVote(savedCandidate.id, savedUser.id, currentYear);
            await User.addVotingHistory(savedUser.id, currentYear, savedCandidate.id);
            console.log('✅ Vote recorded successfully');
        }

        // Test 7: Get vote counts
        console.log('\n7. Testing Vote Count Retrieval...');
        const voteCounts = await Candidate.getVoteCounts();
        console.log('✅ Vote counts retrieved:', voteCounts.length, 'candidates');

        console.log('\n🎉 All tests passed! Migration successful!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }

    // Close the process
    process.exit(0);
}

// Run the test
testMigration();
