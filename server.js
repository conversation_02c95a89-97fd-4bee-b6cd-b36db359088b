const express = require('express')
const app = express();
const cors = require('cors');
const { pool, connectDB } = require('./db');
require('dotenv').config();

app.use(cors());
const bodyParser = require('body-parser'); 
app.use(bodyParser.json()); // req.body
const PORT = process.env.PORT || 3000;

// Import the router files
const userRoutes = require('./routes/userRoutes.js');
const candidateRoutes = require('./routes/candidateRoutes');
const adminRoutes = require('./routes/adminRoutes');
const resultsRoutes = require('./routes/results');
const healthRoutes = require('./routes/healthRoutes');

// Use the routers
app.use('/user', userRoutes);
app.use('/candidate', candidateRoutes);
app.use('/', adminRoutes);
app.use('/', resultsRoutes);
app.use('/', healthRoutes);



app.get('/test', (req, res) => {
    res.send('API is working!');
});

// Initialize database and start server
const startServer = async () => {
    try {
        console.log('🚀 Starting Voting Application...');

        // Connect to database and create tables
        await connectDB();

        // Start the server
        app.listen(PORT, () => {
            console.log(`✅ Server is running on http://localhost:${PORT}`);
            console.log('🎉 Application is ready for use!');
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
    }
};

// Start the application
startServer();