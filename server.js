const express = require('express')
const app = express();
const cors = require('cors');
const { pool, testConnection } = require('./db');
const { initializeDatabase } = require('./database/init');
require('dotenv').config();

app.use(cors());
const bodyParser = require('body-parser'); 
app.use(bodyParser.json()); // req.body
const PORT = process.env.PORT || 3000;

// Import the router files
const userRoutes = require('./routes/userRoutes.js');
const candidateRoutes = require('./routes/candidateRoutes');
const adminRoutes = require('./routes/adminRoutes');
const resultsRoutes = require('./routes/results');
const healthRoutes = require('./routes/healthRoutes');

// Use the routers
app.use('/user', userRoutes);
app.use('/candidate', candidateRoutes);
app.use('/', adminRoutes);
app.use('/', resultsRoutes);
app.use('/', healthRoutes);



app.get('/test', (req, res) => {
    res.send('API is working!');
});

// Initialize database and start server
const startServer = async () => {
    try {
        console.log('🚀 Starting Voting Application...');

        // Test database connection
        await testConnection();

        // Initialize database (create tables, insert initial data)
        await initializeDatabase(pool);

        // Start the server
        app.listen(PORT, () => {
            console.log(`✅ Server is running on http://localhost:${PORT}`);
            console.log('🎉 Application is ready for use!');
            console.log('');
            console.log('📋 Available endpoints:');
            console.log('   • GET  /health              - Health check');
            console.log('   • POST /user/signup         - User registration');
            console.log('   • POST /user/login          - User login');
            console.log('   • GET  /candidate           - Get candidates');
            console.log('   • POST /candidate/vote      - Cast vote');
            console.log('');
            console.log('🔐 Default admin credentials:');
            console.log('   • Aadhar: ************');
            console.log('   • Password: admin123');
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error.message);
        process.exit(1);
    }
};

// Start the application
startServer();