const express = require('express');
const VotingTime = require('../models/votingTime.js');
const {jwtAuthMiddleware, generateToken} = require('../jwt');

const router = express.Router();

router.post('/admin/voting-time', jwtAuthMiddleware, async (req, res) => {
    const { year, startDate, endDate, dailyStartTime, dailyEndTime } = req.body;

    try {
        const votingTime = new VotingTime({
            year,
            startDate,
            endDate,
            dailyStartTime,
            dailyEndTime
        });
        await votingTime.save();

        res.status(201).json({ message: `Voting time for ${year} set successfully!` });
    } catch (error) {
        res.status(500).json({ message: 'Error setting voting time', error: error.message });
    }
});

router.post('/admin/publish-result', jwtAuthMiddleware, async (req, res) => {
    const { year } = req.body;

    try {
        const updatedVotingTime = await VotingTime.findOneAndUpdate(
            { year },
            { resultsPublished: true }
        );

        if (!updatedVotingTime) {
            return res.status(404).json({ message: "No voting time found for this year." });
        }

        res.status(200).json({ message: `Results for ${year} published successfully.` });
    } catch (error) {
        res.status(500).json({ message: 'Error publishing results', error: error.message });
    }
});



module.exports = router;
