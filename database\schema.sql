-- MySQL Database Schema for Voting Application
-- Drop database if exists and create new one
DROP DATABASE IF EXISTS voting_app;
CREATE DATABASE voting_app;
USE voting_app;

-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    age INT NOT NULL,
    email VARCHAR(255),
    mobile VARCHAR(20) NOT NULL,
    address TEXT NOT NULL,
    aadhar_card_number VARCHAR(12) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('voter', 'admin') DEFAULT 'voter',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Candidates table
CREATE TABLE candidates (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    party VARCHAR(255) NOT NULL,
    age INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Votes table (individual vote records)
CREATE TABLE votes (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    candidate_id VARCHAR(36) NOT NULL,
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    year INT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_year (user_id, year)
);

-- Vote counts by year table (for performance optimization)
CREATE TABLE vote_counts_by_year (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    candidate_id VARCHAR(36) NOT NULL,
    year INT NOT NULL,
    count INT DEFAULT 0,
    FOREIGN KEY (candidate_id) REFERENCES candidates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_candidate_year (candidate_id, year)
);

-- Voting history table (user's voting history)
CREATE TABLE voting_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    year INT NOT NULL,
    party_voted VARCHAR(36) NOT NULL, -- This will store candidate_id
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (party_voted) REFERENCES candidates(id) ON DELETE CASCADE
);

-- Voting times table
CREATE TABLE voting_times (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    year INT NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    daily_start_time TIME NOT NULL,
    daily_end_time TIME NOT NULL,
    results_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX idx_users_aadhar ON users(aadhar_card_number);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_votes_user_year ON votes(user_id, year);
CREATE INDEX idx_votes_candidate_year ON votes(candidate_id, year);
CREATE INDEX idx_vote_counts_year ON vote_counts_by_year(year);
CREATE INDEX idx_voting_history_user ON voting_history(user_id);
CREATE INDEX idx_voting_times_year ON voting_times(year);

-- Triggers to automatically update vote counts
DELIMITER //

CREATE TRIGGER update_vote_count_after_insert
AFTER INSERT ON votes
FOR EACH ROW
BEGIN
    INSERT INTO vote_counts_by_year (candidate_id, year, count)
    VALUES (NEW.candidate_id, NEW.year, 1)
    ON DUPLICATE KEY UPDATE count = count + 1;
END//

CREATE TRIGGER update_vote_count_after_delete
AFTER DELETE ON votes
FOR EACH ROW
BEGIN
    UPDATE vote_counts_by_year 
    SET count = count - 1 
    WHERE candidate_id = OLD.candidate_id AND year = OLD.year;
    
    -- Remove record if count becomes 0
    DELETE FROM vote_counts_by_year 
    WHERE candidate_id = OLD.candidate_id AND year = OLD.year AND count <= 0;
END//

DELIMITER ;
