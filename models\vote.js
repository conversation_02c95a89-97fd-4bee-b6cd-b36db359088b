const { DataTypes } = require('sequelize');
const sequelize = require('../db');

// Define the Vote model
const Vote = sequelize.define('Vote', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    candidateId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'candidates',
            key: 'id'
        }
    },
    votedAt: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
    },
    year: {
        type: DataTypes.INTEGER,
        allowNull: false
    }
}, {
    tableName: 'votes',
    timestamps: true
});

module.exports = Vote;
