const express = require('express');
const VotingTime = require('../models/votingTime.js');
const Candidate = require('../models/candidate.js');
const {jwtAuthMiddleware, generateToken} = require('../jwt');

const router = express.Router();

router.get('/results/:year', jwtAuthMiddleware, async (req, res) => {
    const { year } = req.params;

    try {
        const votingTime = await VotingTime.findOne({ year: parseInt(year) });
        if (!votingTime || (!votingTime.results_published && req.user.role !== 'admin')) {
            return res.status(403).json({ message: "Results not available for this year." });
        }

        const result = await Candidate.getVoteCountsByYear(parseInt(year));

        res.status(200).json({ year: parseInt(year), result });
    } catch (error) {
        res.status(500).json({ message: 'Error fetching results', error: error.message });
    }
});

module.exports = router;
