const { v4: uuidv4 } = require('uuid');
const { pool } = require('../db');

class VotingTime {
    constructor(votingTimeData) {
        this.id = votingTimeData.id || uuidv4();
        this.year = votingTimeData.year;
        this.startDate = votingTimeData.startDate;
        this.endDate = votingTimeData.endDate;
        this.dailyStartTime = votingTimeData.dailyStartTime;
        this.dailyEndTime = votingTimeData.dailyEndTime;
        this.resultsPublished = votingTimeData.resultsPublished || false;
    }

    // Validate voting time data
    validate() {
        const errors = [];

        if (!this.year) errors.push('Year is required');
        if (!this.startDate) errors.push('Start date is required');
        if (!this.endDate) errors.push('End date is required');
        if (!this.dailyStartTime) errors.push('Daily start time is required');
        if (!this.dailyEndTime) errors.push('Daily end time is required');

        // Validate date format and logic
        if (this.startDate && this.endDate) {
            const start = new Date(this.startDate);
            const end = new Date(this.endDate);
            if (start >= end) {
                errors.push('End date must be after start date');
            }
        }

        // Validate time format (HH:mm)
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (this.dailyStartTime && !timeRegex.test(this.dailyStartTime)) {
            errors.push('Daily start time must be in HH:mm format');
        }
        if (this.dailyEndTime && !timeRegex.test(this.dailyEndTime)) {
            errors.push('Daily end time must be in HH:mm format');
        }

        return errors;
    }

    // Save voting time to database
    async save() {
        const validationErrors = this.validate();
        if (validationErrors.length > 0) {
            throw new Error(validationErrors.join(', '));
        }

        const query = `
            INSERT INTO voting_times (id, year, start_date, end_date, daily_start_time, daily_end_time, results_published)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            this.id,
            this.year,
            this.startDate,
            this.endDate,
            this.dailyStartTime,
            this.dailyEndTime,
            this.resultsPublished
        ];

        try {
            await pool.execute(query, values);
            return this;
        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                throw new Error('Voting time for this year already exists');
            }
            throw error;
        }
    }

    // Find voting time by year
    static async findOne(criteria) {
        let query = 'SELECT * FROM voting_times WHERE ';
        const conditions = [];
        const values = [];

        for (const [key, value] of Object.entries(criteria)) {
            const dbKey = key === 'startDate' ? 'start_date' :
                         key === 'endDate' ? 'end_date' :
                         key === 'dailyStartTime' ? 'daily_start_time' :
                         key === 'dailyEndTime' ? 'daily_end_time' :
                         key === 'resultsPublished' ? 'results_published' : key;
            conditions.push(`${dbKey} = ?`);
            values.push(value);
        }

        query += conditions.join(' AND ');

        try {
            const [rows] = await pool.execute(query, values);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    // Update voting time
    static async findOneAndUpdate(criteria, updateData) {
        // First find the record
        const existingRecord = await VotingTime.findOne(criteria);
        if (!existingRecord) return null;

        const allowedFields = ['start_date', 'end_date', 'daily_start_time', 'daily_end_time', 'results_published'];
        const updateFields = [];
        const values = [];

        for (const [key, value] of Object.entries(updateData)) {
            const dbKey = key === 'startDate' ? 'start_date' :
                         key === 'endDate' ? 'end_date' :
                         key === 'dailyStartTime' ? 'daily_start_time' :
                         key === 'dailyEndTime' ? 'daily_end_time' :
                         key === 'resultsPublished' ? 'results_published' : key;

            if (allowedFields.includes(dbKey)) {
                updateFields.push(`${dbKey} = ?`);
                values.push(value);
            }
        }

        if (updateFields.length === 0) {
            return existingRecord;
        }

        values.push(existingRecord.id); // Add ID for WHERE clause

        const query = `
            UPDATE voting_times
            SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;

        try {
            await pool.execute(query, values);
            // Return updated record
            return await VotingTime.findOne({ id: existingRecord.id });
        } catch (error) {
            throw error;
        }
    }

    // Check if voting is currently active
    static async isVotingActive(year) {
        const votingTime = await VotingTime.findOne({ year });
        if (!votingTime) return false;

        const now = new Date();
        const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
        const currentTime = now.toTimeString().split(' ')[0].substring(0, 5); // HH:mm

        const startDate = new Date(votingTime.start_date).toISOString().split('T')[0];
        const endDate = new Date(votingTime.end_date).toISOString().split('T')[0];

        // Check if current date is within voting period
        if (currentDate < startDate || currentDate > endDate) {
            return false;
        }

        // Check if current time is within daily voting hours
        if (currentTime < votingTime.daily_start_time || currentTime > votingTime.daily_end_time) {
            return false;
        }

        return true;
    }
}

module.exports = VotingTime;
