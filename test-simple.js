// Simple test script to verify the voting application
require('dotenv').config();
const { pool, connectDB } = require('./db');

async function testApp() {
    console.log('🚀 Testing Voting Application...\n');

    try {
        // Initialize database
        console.log('1. Testing database initialization...');
        await connectDB();
        console.log('✅ Database initialized successfully\n');

        // Test database connection
        console.log('2. Testing database connection...');
        const [rows] = await pool.query('SELECT COUNT(*) as count FROM users');
        console.log('✅ Database connection working, users table has', rows[0].count, 'records\n');

        // Test candidates table
        console.log('3. Testing candidates table...');
        const [candidates] = await pool.query('SELECT COUNT(*) as count FROM candidates');
        console.log('✅ Candidates table has', candidates[0].count, 'records\n');

        console.log('🎉 All tests passed! Application is ready!');
        console.log('');
        console.log('🔐 Default admin credentials:');
        console.log('   • Aadhar: ************');
        console.log('   • Password: admin123');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }

    // Close the process
    process.exit(0);
}

// Run the test
testApp();
