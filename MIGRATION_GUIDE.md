# MongoDB to MySQL Migration Guide

This guide explains how to migrate your voting application from MongoDB to MySQL.

## Prerequisites

1. **MySQL Server**: Install MySQL Server 8.0 or higher
2. **Node.js Dependencies**: The following packages have been updated:
   - Removed: `mongoose`
   - Added: `mysql2`, `uuid`

## Database Setup

### 1. Create MySQL Database

```bash
# Login to MySQL
mysql -u root -p

# Run the schema file
source database/schema.sql
```

Or manually execute the SQL commands in `database/schema.sql`

### 2. Environment Configuration

Copy `.env.sample` to `.env` and update the MySQL configuration:

```env
PORT=3000

# MySQL Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=voting_app
DB_PORT=3306

# JWT Secret
JWT_SECRET=your_jwt_secret_key_here
```

## Key Changes Made

### 1. Database Connection (`db.js`)
- Replaced Mongoose connection with MySQL2 connection pool
- Added connection testing and error handling

### 2. Models
All models have been converted from Mongoose schemas to JavaScript classes with raw SQL queries:

#### User Model (`models/user.js`)
- Uses UUID v4 for primary keys
- Manual password hashing with bcrypt
- Raw SQL queries for CRUD operations
- Separate voting history table

#### Candidate Model (`models/candidate.js`)
- UUID v4 primary keys
- Separate votes and vote_counts_by_year tables
- Automatic vote counting with MySQL triggers

#### VotingTime Model (`models/votingTime.js`)
- Date and time validation
- Raw SQL operations

### 3. Routes Updated

#### User Routes (`routes/userRoutes.js`)
- Updated signup, login, profile, and password change routes
- Uses new User model methods

#### Candidate Routes (`routes/candidateRoutes.js`)
- Updated CRUD operations for candidates
- Completely rewritten voting logic
- Updated vote counting

#### Admin Routes (`routes/adminRoutes.js`)
- Updated voting time management
- Results publishing functionality

#### Results Routes (`routes/results.js`)
- Updated to use MySQL queries for vote results

## Database Schema

### Tables Created:
1. **users** - User information with UUID primary keys
2. **candidates** - Candidate information
3. **votes** - Individual vote records
4. **vote_counts_by_year** - Aggregated vote counts (updated by triggers)
5. **voting_history** - User voting history
6. **voting_times** - Voting period configuration

### Key Features:
- UUID v4 primary keys for all tables
- Foreign key constraints
- Automatic vote counting with MySQL triggers
- Indexes for performance optimization
- Unique constraints to prevent duplicate votes

## Migration Steps

1. **Backup MongoDB Data** (if needed for reference)
2. **Install Dependencies**: `npm install`
3. **Setup MySQL Database**: Run `database/schema.sql`
4. **Update Environment**: Configure `.env` file
5. **Test Application**: Start with `npm start`

## API Endpoints (Unchanged)

All API endpoints remain the same:
- `POST /user/signup` - User registration
- `POST /user/login` - User login
- `GET /user/profile` - Get user profile
- `PUT /user/profile/password` - Change password
- `POST /candidate` - Add candidate (admin only)
- `PUT /candidate/:id` - Update candidate (admin only)
- `DELETE /candidate/:id` - Delete candidate (admin only)
- `GET /candidate` - Get all candidates
- `POST /candidate/vote` - Cast vote
- `GET /candidate/total-vote-count` - Get vote counts
- `POST /admin/voting-time` - Set voting time (admin only)
- `POST /admin/publish-result` - Publish results (admin only)
- `GET /results/:year` - Get results for a year

## Testing

After migration, test the following:
1. User registration and login
2. Candidate CRUD operations (admin)
3. Voting functionality
4. Vote counting and results
5. Voting time management

## Troubleshooting

### Common Issues:
1. **Connection Error**: Check MySQL credentials in `.env`
2. **UUID Issues**: Ensure MySQL 8.0+ for UUID() function
3. **Foreign Key Errors**: Ensure proper data relationships
4. **Trigger Issues**: Check MySQL trigger syntax support

### Performance Notes:
- MySQL triggers automatically maintain vote counts
- Indexes are created for frequently queried fields
- Connection pooling is used for better performance
